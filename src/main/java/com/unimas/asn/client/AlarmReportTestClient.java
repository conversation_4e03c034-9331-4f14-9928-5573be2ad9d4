package com.unimas.asn.client;

import com.unimas.asn.codec.OssCoerAdapter;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.oss.asn1.IA5String;
import com.oss.asn1.Uint64;
import com.oss.asn1.Uint8;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 告警上报测试客户端
 * 用于测试AlarmReportServer是否正常工作
 */
public class AlarmReportTestClient {
    private static final Logger logger = LoggerFactory.getLogger(AlarmReportTestClient.class);
    
    private final String serverUrl;
    
    public AlarmReportTestClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }
    
    /**
     * 发送测试告警报告
     */
    public void sendTestAlarmReport() {
        try {
            // 创建测试告警报告
            AlarmReportRequest request = createTestAlarmReport();
            
            // 创建消息帧
            MessageRequestFrame frame = new MessageRequestFrame();
            frame.setVersion(new Uint8(1));
            frame.setContent(new MessageRequestFrame.Content());
            frame.getContent().setAlarmReportRequest(request);
            
            // 编码消息
            byte[] requestData = OssCoerAdapter.encode(frame);
            logger.info("Sending alarm report: {}", Hex.toHexString(requestData));
            
            // 发送HTTP请求
            byte[] responseData = sendHttpRequest(requestData);
            logger.info("Received response: {}", Hex.toHexString(responseData));
            
            // 解码响应
            MessageResponseFrame responseFrame = OssCoerAdapter.decode(responseData, MessageResponseFrame.class);
            logger.info("Decoded response: {}", responseFrame);
            
            if (responseFrame.getContent() != null && responseFrame.getContent().hasAlarmReportResponse()) {
                AlarmReportResponse response = responseFrame.getContent().getAlarmReportResponse();
                logger.info("Alarm report sent successfully for service: {}", response.getServiceId());
            }
            
        } catch (Exception e) {
            logger.error("Failed to send test alarm report", e);
        }
    }
    
    /**
     * 创建测试告警报告
     */
    private AlarmReportRequest createTestAlarmReport() {
        AlarmReportRequest request = new AlarmReportRequest();
        request.setMessageType(ContentMessageType.reportAlarm);
        request.setServiceId(new ServiceId((short) 1001));
        
        // 创建告警项列表
        AlarmReportRequest.Alarms alarms = new AlarmReportRequest.Alarms();
        
        // 添加第一个告警
        AlarmItem alarm1 = new AlarmItem();
        alarm1.setAlarmType(AlarmType.securityAlarm);
        alarm1.setAlarmCode(AlarmCode.illegalCertificate);
        alarm1.setAlarmDesc(new IA5String("测试安全告警：非法证书"));
        alarm1.setAlarmCount(new Uint64(new BigInteger("1")));
        alarms.add(alarm1);
        
        // 添加第二个告警
        AlarmItem alarm2 = new AlarmItem();
        alarm2.setAlarmType(AlarmType.faultAlarm);
        alarm2.setAlarmCode(AlarmCode.channelException);
        alarm2.setAlarmDesc(new IA5String("测试故障告警：通道异常"));
        alarm2.setAlarmCount(new Uint64(new BigInteger("3")));
        alarms.add(alarm2);
        
        // 添加第三个告警
        AlarmItem alarm3 = new AlarmItem();
        alarm3.setAlarmType(AlarmType.securityAlarm);
        alarm3.setAlarmCode(AlarmCode.protocolVerifyFailure);
        alarm3.setAlarmDesc(new IA5String("测试安全告警：协议验证失败"));
        alarm3.setAlarmCount(new Uint64(new BigInteger("2")));
        alarms.add(alarm3);
        
        request.setAlarms(alarms);
        return request;
    }
    
    /**
     * 发送HTTP请求
     */
    private byte[] sendHttpRequest(byte[] requestData) throws IOException {
        URL url = new URL(serverUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // 设置请求属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/octet-stream");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            
            // 发送请求数据
            try (OutputStream os = connection.getOutputStream()) {
                os.write(requestData);
                os.flush();
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream is = connection.getInputStream()) {
                    return readAllBytes(is);
                }
            } else {
                throw new IOException("HTTP request failed with response code: " + responseCode);
            }
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * JDK 8兼容的读取所有字节方法
     */
    private byte[] readAllBytes(InputStream is) throws IOException {
        byte[] buffer = new byte[8192];
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        int bytesRead;
        while ((bytesRead = is.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        return baos.toByteArray();
    }
    
    /**
     * 主方法，用于运行测试客户端
     */
    public static void main(String[] args) {
        // 默认服务器URL
        String serverUrl = "http://localhost:8080/alarm";
        
        // 解析命令行参数
        if (args.length >= 1) {
            serverUrl = args[0];
        }
        
        logger.info("Testing alarm report server at: {}", serverUrl);
        
        AlarmReportTestClient client = new AlarmReportTestClient(serverUrl);
        
        // 发送测试告警报告
        client.sendTestAlarmReport();
        
        logger.info("Test completed");
    }
}
